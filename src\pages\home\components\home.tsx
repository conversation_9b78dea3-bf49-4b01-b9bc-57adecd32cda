import React, { memo } from 'react'
import { getPageSeoConfig } from '@/utils/seoI18n'
import SEOHead from '@/components/seo/SEOHead'
import HomePage from './HomePage'
import { getCurrentLanguage } from '@/utils'

const Home = () => {
  const storedLang = getCurrentLanguage();
  const seoConfig = getPageSeoConfig('home', storedLang)

  return (
    <>
      <SEOHead
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        type="website"
      />
      <HomePage />
    </>
  )
}

export default memo(Home)
