import { useRouter } from 'next/router'
import { useMemo } from 'react'
import { ROUTES } from '@/constants'
import { getPageSeoConfig } from '@/utils/seoI18n'
import SEOHead from '@/components/seo/SEOHead'

const NextHead = () => {
  const { pathname, locale } = useRouter()

  // Map router → SEO config
  const seoConfig = useMemo(() => {
    // Admin pages - always noindex
    if (pathname.startsWith('/admin')) {
      const adminTitles: Record<string, string> = {
        [ROUTES.adminDashboard]: 'ダッシュボード',
        [ROUTES.adminCollections]: 'コレクション管理',
        [ROUTES.adminNfts]: 'NFT管理',
        [ROUTES.adminBanners]: 'バナー管理',
        [ROUTES.adminUsers]: 'ユーザー管理',
        [ROUTES.adminTransactions]: '取引管理',
        [ROUTES.adminNews]: 'ニュース管理',
        [ROUTES.adminAdminAccounts]: 'アカウント管理',
      }

      const matched = Object.keys(adminTitles).find((key) => pathname.startsWith(key))
      return {
        title: matched ? adminTitles[matched] : 'Admin',
        description: 'Admin panel for GLITTERS marketplace',
        keywords: 'admin, management, GLITTERS',
        noIndex: true
      }
    }

    // Public pages - use i18n SEO config
    const pageMap: Record<string, string> = {
      [ROUTES.privacyPolicy]: 'privacy',
      [ROUTES.termsOfUse]: 'terms',
      [ROUTES.collections]: 'collections',
      [ROUTES.news]: 'news',
      [ROUTES.contact]: 'contact',
      [ROUTES.aboutUs]: 'about',
    }

    const matched = Object.keys(pageMap).find((key) => pathname.startsWith(key))
    if (matched) {
      return getPageSeoConfig(pageMap[matched], locale)
    }

    // Default fallback
    return getPageSeoConfig('home', locale)
  }, [pathname, locale])

  return (
    <SEOHead
      title={seoConfig.title}
      description={seoConfig.description}
      keywords={seoConfig.keywords}
      noIndex={seoConfig.noIndex}
    />
  )
}

export default NextHead
