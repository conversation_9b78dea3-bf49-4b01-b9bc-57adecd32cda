import { useRouter } from 'next/router'
import enSeo from '../../public/locales/en/seo.json'
import jaSeo from '../../public/locales/ja/seo.json'

type SeoTranslations = typeof enSeo

// SEO translations object
const seoTranslations: Record<string, SeoTranslations> = {
  en: enSeo,
  ja: jaSeo,
}

// Hook to get SEO translations based on current locale
export const useSeoTranslations = (): SeoTranslations => {
  const { locale } = useRouter()
  return seoTranslations[locale || 'en'] || seoTranslations.en
}

// Function to get SEO translations for server-side usage
export const getSeoTranslations = (locale: string = 'en'): SeoTranslations => {
  return seoTranslations[locale] || seoTranslations.en
}

// Function to interpolate variables in translation strings
export const interpolateString = (template: string, variables: Record<string, string>): string => {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key] || match
  })
}

// Get page-specific SEO config
export const getPageSeoConfig = (
  page: string,
  locale: string = 'en',
  variables?: Record<string, string>
) => {
  const translations = getSeoTranslations(locale)
  
  const configs: Record<string, any> = {
    home: {
      title: translations.home_title,
      description: translations.home_description,
      keywords: translations.home_keywords,
    },
    collections: {
      title: translations.collections_title,
      description: translations.collections_description,
      keywords: translations.collections_keywords,
    },
    news: {
      title: translations.news_title,
      description: translations.news_description,
      keywords: translations.news_keywords,
    },
    contact: {
      title: translations.contact_title,
      description: translations.contact_description,
      keywords: translations.contact_keywords,
    },
    about: {
      title: translations.about_title,
      description: translations.about_description,
      keywords: translations.about_keywords,
    },
    privacy: {
      title: translations.privacy_title,
      description: translations.privacy_description,
      keywords: translations.privacy_keywords,
    },
    terms: {
      title: translations.terms_title,
      description: translations.terms_description,
      keywords: translations.terms_keywords,
    },
    collectionDetail: {
      title: interpolateString(translations.collection_detail_title, variables || {}),
      description: interpolateString(translations.collection_detail_description, variables || {}),
      keywords: interpolateString(translations.collection_detail_keywords, variables || {}),
    },
    newsDetail: {
      title: interpolateString(translations.news_detail_title, variables || {}),
      description: interpolateString(translations.news_detail_description, variables || {}),
      keywords: interpolateString(translations.news_detail_keywords, variables || {}),
    },
  }

  return configs[page] || {
    title: translations.site_name,
    description: translations.site_description,
    keywords: translations.site_keywords,
  }
}

// Get site-wide SEO config
export const getSiteSeoConfig = (locale: string = 'en') => {
  const translations = getSeoTranslations('ja')
  
  console.log({translations, locale, 
    siteName: translations.site_name,
    siteDescription: translations.site_description,
    siteKeywords: translations.site_keywords,
    siteUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://glitters.io',
    twitterHandle: '@glitters_nft',
    locale1: locale === 'ja' ? 'ja_JP' : 'en_US',
    type: 'website',
  });
  return {
    siteName: translations.site_name,
    siteDescription: translations.site_description,
    siteKeywords: translations.site_keywords,
    siteUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://glitters.io',
    twitterHandle: '@glitters_nft',
    locale: locale === 'ja' ? 'ja_JP' : 'en_US',
    type: 'website',
  }
}

// Generate hreflang links for multilingual SEO
export const getHreflangLinks = (pathname: string, baseUrl: string) => {
  const locales = ['en', 'ja']
  
  return locales.map(locale => ({
    rel: 'alternate',
    hrefLang: locale,
    href: locale === 'en' ? `${baseUrl}${pathname}` : `${baseUrl}/${locale}${pathname}`,
  }))
}

// Generate canonical URL based on locale
export const getCanonicalUrl = (pathname: string, locale: string, baseUrl: string) => {
  if (locale === 'en') {
    return `${baseUrl}${pathname}`
  }
  return `${baseUrl}/${locale}${pathname}`
}
