import { GetServerSideProps } from 'next'
import { pageHeadConfigDefault } from '@/constants/header'

function generateRobotsTxt(baseUrl: string): string {
  return `# Robots.txt for GLITTERS NFT Marketplace
# Generated automatically

User-agent: *
Allow: /

# Disallow admin pages
Disallow: /admin/
Disallow: /admin/*

# Disallow user private pages
Disallow: /my-profile/
Disallow: /my-nfts/
Disallow: /my-transactions/
Disallow: /my-info/

# Disallow API routes
Disallow: /api/

# Disallow Next.js internal routes
Disallow: /_next/
Disallow: /_error
Disallow: /404
Disallow: /500

# Allow important pages
Allow: /
Allow: /collections
Allow: /collections/*
Allow: /news
Allow: /news/*
Allow: /contact
Allow: /about-us
Allow: /privacy-policy
Allow: /terms-of-use

# Crawl delay (optional - adjust based on server capacity)
Crawl-delay: 1

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Additional sitemaps (if you create them)
# Sitemap: ${baseUrl}/collections-sitemap.xml
# Sitemap: ${baseUrl}/news-sitemap.xml`
}

// Default export to prevent Next.js errors
function RobotsTxt() {
  // getServerSideProps will do the heavy lifting
  return null
}

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  const baseUrl = pageHeadConfigDefault.siteUrl

  // Generate the robots.txt content
  const robotsTxt = generateRobotsTxt(baseUrl)

  res.setHeader('Content-Type', 'text/plain')
  res.setHeader('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate')
  res.write(robotsTxt)
  res.end()

  return {
    props: {},
  }
}

export default RobotsTxt
