import React from 'react'
import { useRouter } from 'next/router'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'
import { getNewsDetail } from '@/services/apiCall/news'
import { TNewsItem } from '@/interfaces/news'
import { LeftOutlined } from '@ant-design/icons'
import { Spin } from 'antd'
import { StaticPageCard } from '@/components/card/StaticPageCard'
import { useTranslate } from '@/hooks'
import { StaticPageLayout } from '@/components/layouts/staticPageLayout'
import SEOHead from '@/components/seo/SEOHead'
import { getPageSeoConfig } from '@/utils/seoI18n'
import { getCurrentLanguage } from '@/utils'

const NewsDetailPage = () => {
  const router = useRouter()
  const storedLang = getCurrentLanguage();
  const newsId = router.query.newsId as string
  const trans = useTranslate()

  const { data: news, isLoading } = useQuery<TNewsItem | undefined>({
    queryKey: ['news_detail', newsId],
    queryFn: () => getNewsDetail(newsId),
    enabled: !!newsId,
  })

  if (isLoading || !news) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Spin size="large" />
      </div>
    )
  }
  // Extract text content for meta description
  const textContent = news.context?.replace(/<[^>]*>/g, '') || ''
  const metaDescription = textContent.length > 160
    ? textContent.substring(0, 157) + '...'
    : textContent

  // Get localized SEO config for news detail
  const baseSeoConfig = getPageSeoConfig('newsDetail', storedLang, { newsTitle: news.title })

  return (
    <>
      <SEOHead
        title={news.title}
        description={metaDescription || baseSeoConfig.description}
        keywords={`${news.title}, ${baseSeoConfig.keywords}`}
        type="article"
        image={news.imageUrl}
        publishedTime={news.createdAt}
        modifiedTime={news.updatedAt || news.createdAt}
        author="GLITTERS"
        section="News"
      />
      <StaticPageLayout title={trans.news}>
        <>
          <StaticPageCard
            imageUrl={news.imageUrl}
            title={news.title}
            createdAt={news.createdAt}
          >
            <div
              className="tinymce-content new-content"
              dangerouslySetInnerHTML={{ __html: news.context }}
            />
          </StaticPageCard>
          <div className="flex justify-center mt-8">
            <Link href="/news" className="text-primary gap-2 hover:underline">
              <LeftOutlined rev={undefined} />
              {trans.see_more_news}
            </Link>
          </div>
        </>
      </StaticPageLayout>
    </>
  )
}

export default NewsDetailPage
