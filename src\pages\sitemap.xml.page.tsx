import { GetServerSideProps } from 'next'
import { pageHeadConfigDefault } from '@/constants/header'

// This function generates the sitemap XML with multilingual support
function generateSiteMap(
  collections: any[],
  news: any[],
  baseUrl: string
): string {
  const locales = ['en', 'ja']
  const staticPages = [
    { path: '', priority: '1.0', changefreq: 'daily' },
    { path: '/collections', priority: '0.9', changefreq: 'daily' },
    { path: '/news', priority: '0.8', changefreq: 'daily' },
    { path: '/contact', priority: '0.7', changefreq: 'monthly' },
    { path: '/about-us', priority: '0.6', changefreq: 'monthly' },
    { path: '/privacy-policy', priority: '0.5', changefreq: 'yearly' },
    { path: '/terms-of-use', priority: '0.5', changefreq: 'yearly' },
  ]

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
  <!-- Static pages with multilingual support -->
  ${staticPages.map(page => {
    return locales.map(locale => {
      const url = locale === 'en' ? `${baseUrl}${page.path}` : `${baseUrl}/${locale}${page.path}`
      const alternateLinks = locales.map(altLocale => {
        const altUrl = altLocale === 'en' ? `${baseUrl}${page.path}` : `${baseUrl}/${altLocale}${page.path}`
        return `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />`
      }).join('\n')

      return `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
${alternateLinks}
  </url>`
    }).join('\n')
  }).join('\n')}

  <!-- Dynamic collection pages with multilingual support -->
  ${collections
    .map((collection) => {
      return locales.map(locale => {
        const url = locale === 'en' ? `${baseUrl}/collections/${collection.id}` : `${baseUrl}/${locale}/collections/${collection.id}`
        const alternateLinks = locales.map(altLocale => {
          const altUrl = altLocale === 'en' ? `${baseUrl}/collections/${collection.id}` : `${baseUrl}/${altLocale}/collections/${collection.id}`
          return `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />`
        }).join('\n')

        return `  <url>
    <loc>${url}</loc>
    <lastmod>${collection.updatedAt || collection.createdAt}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
${alternateLinks}
  </url>`
      }).join('\n')
    })
    .join('\n')}

  <!-- Dynamic news pages with multilingual support -->
  ${news
    .map((article) => {
      return locales.map(locale => {
        const url = locale === 'en' ? `${baseUrl}/news/${article.id}` : `${baseUrl}/${locale}/news/${article.id}`
        const alternateLinks = locales.map(altLocale => {
          const altUrl = altLocale === 'en' ? `${baseUrl}/news/${article.id}` : `${baseUrl}/${altLocale}/news/${article.id}`
          return `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />`
        }).join('\n')

        return `  <url>
    <loc>${url}</loc>
    <lastmod>${article.updatedAt || article.createdAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
${alternateLinks}
  </url>`
      }).join('\n')
    })
    .join('\n')}
</urlset>`
}

// Default export to prevent Next.js errors
function SiteMap() {
  // getServerSideProps will do the heavy lifting
  return null
}

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  const baseUrl = pageHeadConfigDefault.siteUrl

  try {
    // Fetch collections and news data
    // Note: You'll need to implement these API calls based on your backend
    const collectionsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/v1/market/collections?pageSize=1000`)
    const newsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/v1/news?pageSize=1000`)
    
    const collections = collectionsResponse.ok ? (await collectionsResponse.json()).items || [] : []
    const news = newsResponse.ok ? (await newsResponse.json()).items || [] : []

    // Generate the XML sitemap
    const sitemap = generateSiteMap(collections, news, baseUrl)

    res.setHeader('Content-Type', 'text/xml')
    res.setHeader('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate')
    res.write(sitemap)
    res.end()

    return {
      props: {},
    }
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Generate basic sitemap without dynamic content
    const basicSitemap = generateSiteMap([], [], baseUrl)
    
    res.setHeader('Content-Type', 'text/xml')
    res.setHeader('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate')
    res.write(basicSitemap)
    res.end()

    return {
      props: {},
    }
  }
}

export default SiteMap
