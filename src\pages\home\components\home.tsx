import React, { memo } from 'react'
import { useRouter } from 'next/router'
import { getHomepageStructuredData } from '@/utils/seo'
import { getPageSeoConfig } from '@/utils/seoI18n'
import SEOHead from '@/components/seo/SEOHead'
import HomePage from './HomePage'

const Home = () => {
  const { locale } = useRouter()
  const seoConfig = getPageSeoConfig('home', locale)
  const structuredData = getHomepageStructuredData()

  return (
    <>
      <SEOHead
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        type="website"
        structuredData={structuredData}
      />
      <HomePage />
    </>
  )
}

export default memo(Home)
