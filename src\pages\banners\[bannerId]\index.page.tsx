import React, { useState } from 'react'
import { Empty } from 'antd'
import Image from 'next/image'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { useQuery } from '@tanstack/react-query'
import Breadcrumbs from '@/components/breadcrumbs/Breadcrumbs'
import { NftCard } from '@/components/card/NftCard'
import { CARD_SIZES } from '@/constants/enum'
import { getBannerDetail } from '@/services/apiCall/banner'
import { getCollectionNftsForUser } from '@/services/apiCall/nft'
import { TBanner } from '@/interfaces/banner'
import { PaginatedNftDetail } from '@/interfaces/nft'
import { useTranslate } from '@/hooks'
import { UiPagination } from '@/components/ui/Pagination'

const DEFAULT_PAGE_SIZE = 8
const DEFAULT_PAGE_INDEX = 1

const BannerDetailPage = () => {
  const router = useRouter()
  const bannerId = router?.query?.bannerId as string
  const trans = useTranslate()
  const [pageIndex, setPageIndex] = useState(DEFAULT_PAGE_INDEX)
  const [pageSize] = useState(DEFAULT_PAGE_SIZE)

  // Fetch banner detail
  const { data: bannerData } = useQuery({
    queryKey: ['banner_detail', bannerId],
    queryFn: (): Promise<TBanner> => getBannerDetail(bannerId),
    enabled: !!bannerId,
  })

  // Fetch related NFTs by collection
  const { data: nftsData, isSuccess: nftsLoaded } = useQuery({
    queryKey: ['nfts_for_banner', bannerData?.collection, pageIndex, pageSize],
    queryFn: (): Promise<PaginatedNftDetail> =>
      getCollectionNftsForUser(
        {
          pageIndex,
          pageSize,
        },
        bannerData?.collection as string
      ),
    enabled: !!bannerData?.collection,
  })

  const handlePageChange = (page: number) => {
    setPageIndex(page)
  }

  return (
    <div className="text-white flex flex-col">
      <Head>
        <title>{bannerData?.title || 'Banner Detail'} | Glitters</title>
        <meta name="description" content={bannerData?.description || ''} />
      </Head>
      <div className="max-w-7xl mx-auto w-full">
        {/* Breadcrumbs */}
        <Breadcrumbs dynamicNames={[bannerData?.title || 'Banner Title']} />

        <div className="bg-getCardBg rounded-[8px] p-[20px] mt-6">
          {/* Banner Image */}
          {bannerData?.image && (
            <div className="w-full rounded-lg overflow-hidden sm:max-h-[500px] max-h-[150px]">
              <Image
                src={bannerData.image}
                alt={bannerData.title}
                width={1200}
                height={480}
                className="w-full object-cover rounded-lg"
                priority
              />
            </div>
          )}
          {/* Banner Description */}
          {bannerData?.description && (
            <pre className="pt-10 sm:mb-10 font-montserrat font-medium text-[14px] leading-[170%] tracking-[0.02em] text-white text-wrap">
              {bannerData.description}
            </pre>
          )}
        </div>

        {/* Related NFTs Section */}
        <div className="mb-[30px] mt-8 flex items-center justify-between border-b border-1 border-default">
          <h2 className="font-montserrat font-semibold text-[18px] leading-[170%] tracking-[0.02em] text-white mb-4">
            {trans.list_of_related_nft}
          </h2>
        </div>
        {nftsLoaded && nftsData?.items?.length ? (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-[50px]">
              {nftsData.items.map((nft) => (
                <NftCard
                  key={nft._id}
                  name={nft.name}
                  imageUrl={nft.canSetAsProfileImage ? nft.image : ''}
                  {...(nft.isVideo
                    ? { videoUrl: nft.image }
                    : { imageUrl: nft.image })}
                  price={nft.price?.toString() || ''}
                  size={CARD_SIZES.MD}
                  href={`/collections/${nft.collection._id}/${nft._id}`}
                />
              ))}
            </div>
            <div className="flex justify-center mt-8">
              <UiPagination
                current={pageIndex}
                pageSize={pageSize}
                total={nftsData.totalItems}
                onChange={handlePageChange}
                showSizeChanger={false}
                showLessItems
              />
            </div>
          </>
        ) : (
          <div className="flex justify-center items-center min-h-[200px]">
            <Empty description={trans.no_nfts_found} />
          </div>
        )}
      </div>
    </div>
  )
}

export default BannerDetailPage
