import Head from 'next/head'
import { useRouter } from 'next/router'
import { getSiteSeoConfig, getHreflangLinks, getCanonicalUrl } from '@/utils/seoI18n'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
  noIndex?: boolean
  noFollow?: boolean
  canonical?: string
  structuredData?: object
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags,
  noIndex = false,
  noFollow = false,
  canonical,
  structuredData,
}) => {
  const router = useRouter()
  const { locale, asPath } = router
    if (typeof window === 'undefined') {
    return 'en' // Default for SSR
  }

  const storedLang = localStorage.getItem(LANGUAGE_STORAGE_KEY)
  return (storedLang || 'en') as 'en' | 'ja'
  console.log(storedLang);
  // Get locale-specific site config
  const siteConfig = getSiteSeoConfig(locale)

  // Build full URL
  const baseUrl = siteConfig.siteUrl
  const fullUrl = url || getCanonicalUrl(asPath, locale || 'en', baseUrl)
  const canonicalUrl = canonical || fullUrl

  // Build title with site name
  const fullTitle = title
    ? `${title} | ${siteConfig.siteName}`
    : `${siteConfig.siteName} - ${siteConfig.siteDescription}`

  // Default image
  const ogImage = image || `${baseUrl}/images/og-image.jpg`

  // Build robots content
  const robotsContent: ('noindex' | 'index' | 'nofollow' | 'follow')[] = []
  if (noIndex) robotsContent.push('noindex')
  else robotsContent.push('index')
  if (noFollow) robotsContent.push('nofollow')
  else robotsContent.push('follow')

  // Build keywords
  const allKeywords = keywords
    ? `${keywords}, ${siteConfig.siteKeywords}`
    : siteConfig.siteKeywords

  // Generate hreflang links
  const hreflangLinks = getHreflangLinks(asPath, baseUrl)

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description || siteConfig.siteDescription} />
      <meta name="keywords" content={allKeywords} />
      <meta name="robots" content={robotsContent.join(', ')} />
      <meta name="googlebot" content="max-video-preview:-1, max-image-preview:large, max-snippet:-1" />

      {/* Language and Canonical URLs */}
      <link rel="canonical" href={canonicalUrl} />
      {hreflangLinks.map((link, index) => (
        <link key={index} rel={link.rel} hrefLang={link.hrefLang} href={link.href} />
      ))}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title || siteConfig.siteName} />
      <meta property="og:description" content={description || siteConfig.siteDescription} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={title || siteConfig.siteName} />
      <meta property="og:site_name" content={siteConfig.siteName} />
      <meta property="og:locale" content={siteConfig.locale} />
      {locale === 'ja' && <meta property="og:locale:alternate" content="en_US" />}
      {locale === 'en' && <meta property="og:locale:alternate" content="ja_JP" />}
      
      {/* Article specific Open Graph tags */}
      {type === 'article' && (
        <>
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {author && <meta property="article:author" content={author} />}
          {section && <meta property="article:section" content={section} />}
          {tags && tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={siteConfig.twitterHandle} />
      <meta name="twitter:creator" content={siteConfig.twitterHandle} />
      <meta name="twitter:title" content={title || siteConfig.siteName} />
      <meta name="twitter:description" content={description || siteConfig.siteDescription} />
      <meta name="twitter:image" content={ogImage} />
      <meta name="twitter:image:alt" content={title || siteConfig.siteName} />
      
      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#f2b516" />
      <meta name="msapplication-TileColor" content="#f2b516" />
      <meta name="application-name" content={siteConfig.siteName} />

      {/* Favicon */}
      <link rel="icon" href="/favicon.png" />
      <link rel="apple-touch-icon" href="/favicon.png" />

      {/* Language and Region */}
      <meta httpEquiv="content-language" content={locale || 'en'} />
      <meta name="geo.region" content="JP" />
      <meta name="geo.country" content="Japan" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}
      
      {/* Google Site Verification */}
      {process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION && (
        <meta
          name="google-site-verification"
          content={process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION}
        />
      )}
    </Head>
  )
}

export default SEOHead
