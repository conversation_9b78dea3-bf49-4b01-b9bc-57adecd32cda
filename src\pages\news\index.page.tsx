import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { getListNewsForUser } from '@/services/apiCall/news'
import { useTranslate } from '@/hooks'
import { getPageSeoConfig } from '@/utils/seoI18n'
import SEOHead from '@/components/seo/SEOHead'
import NewsList from './NewsList'
import { Spin } from 'antd'
import { getCurrentLanguage } from '@/utils'

const DEFAULT_PAGE_SIZE = 12

export default function NewsPage() {
  const t = useTranslate()
  const storedLang = getCurrentLanguage();
  const [currentPage, setCurrentPage] = useState(1)
  const seoConfig = getPageSeoConfig('news', storedLang)

  const { data, isLoading } = useQuery({
    queryKey: ['news', currentPage],
    queryFn: () =>
      getListNewsForUser({
        pageIndex: currentPage,
        pageSize: DEFAULT_PAGE_SIZE,
      }),
  })

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <>
      <SEOHead
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        type="website"
      />
      <div>
        <h1 className="font-montserrat font-semibold sm:text-[48px] text-[32px] leading-[120%] tracking-[0.02em] text-white sm:mb-10 mb-6">
          {t.news}
        </h1>

        {isLoading ? (
          <div className="flex justify-center items-center min-h-[400px]">
            <Spin size="large" />
          </div>
        ) : (
          data && (
            <NewsList
              items={data.items}
              totalItems={data.totalItems}
              pageSize={data.pageSize}
              currentPage={currentPage}
              onPageChange={handlePageChange}
            />
          )
        )}
      </div>
    </>
  )
}
