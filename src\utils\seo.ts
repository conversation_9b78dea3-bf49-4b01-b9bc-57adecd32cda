import { pageHeadConfigDefault } from '@/constants/header'

// Organization Schema
export const getOrganizationSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'GLITTERS',
  description: pageHeadConfigDefault.description,
  url: pageHeadConfigDefault.siteUrl,
  logo: `${pageHeadConfigDefault.siteUrl}/logos/logo_brand.png`,
  sameAs: [
    'https://twitter.com/glitters_nft',
    'https://facebook.com/glitters',
    'https://instagram.com/glitters_nft',
  ],
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'Customer Service',
    url: `${pageHeadConfigDefault.siteUrl}/contact`,
  },
})

// Website Schema
export const getWebsiteSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: pageHeadConfigDefault.siteName,
  description: pageHeadConfigDefault.description,
  url: pageHeadConfigDefault.siteUrl,
  potentialAction: {
    '@type': 'SearchAction',
    target: {
      '@type': 'EntryPoint',
      urlTemplate: `${pageHeadConfigDefault.siteUrl}/collections?search={search_term_string}`,
    },
    'query-input': 'required name=search_term_string',
  },
})

// NFT Collection Schema
export const getCollectionSchema = (collection: any) => ({
  '@context': 'https://schema.org',
  '@type': 'CreativeWork',
  name: collection.name,
  description: collection.description,
  image: collection.logoImage,
  creator: {
    '@type': 'Person',
    name: collection.creatorName || 'GLITTERS Creator',
  },
  dateCreated: collection.createdAt,
  url: `${pageHeadConfigDefault.siteUrl}/collections/${collection.id}`,
  isPartOf: {
    '@type': 'WebSite',
    name: pageHeadConfigDefault.siteName,
    url: pageHeadConfigDefault.siteUrl,
  },
})

// NFT Item Schema
export const getNFTSchema = (nft: any) => ({
  '@context': 'https://schema.org',
  '@type': 'DigitalDocument',
  name: nft.name,
  description: nft.description,
  image: nft.imageUrl,
  creator: {
    '@type': 'Person',
    name: nft.creatorName || 'GLITTERS Creator',
  },
  dateCreated: nft.createdAt,
  url: `${pageHeadConfigDefault.siteUrl}/nfts/${nft.id}`,
  isPartOf: {
    '@type': 'Collection',
    name: nft.collectionName,
    url: `${pageHeadConfigDefault.siteUrl}/collections/${nft.collectionId}`,
  },
  offers: nft.price ? {
    '@type': 'Offer',
    price: nft.price,
    priceCurrency: 'ETH',
    availability: 'https://schema.org/InStock',
  } : undefined,
})

// News Article Schema
export const getNewsSchema = (article: any) => ({
  '@context': 'https://schema.org',
  '@type': 'NewsArticle',
  headline: article.title,
  description: article.description || article.excerpt,
  image: article.imageUrl,
  datePublished: article.createdAt,
  dateModified: article.updatedAt || article.createdAt,
  author: {
    '@type': 'Organization',
    name: pageHeadConfigDefault.siteName,
    url: pageHeadConfigDefault.siteUrl,
  },
  publisher: {
    '@type': 'Organization',
    name: pageHeadConfigDefault.siteName,
    logo: {
      '@type': 'ImageObject',
      url: `${pageHeadConfigDefault.siteUrl}/logos/logo_brand.png`,
    },
  },
  url: `${pageHeadConfigDefault.siteUrl}/news/${article.id}`,
  mainEntityOfPage: {
    '@type': 'WebPage',
    '@id': `${pageHeadConfigDefault.siteUrl}/news/${article.id}`,
  },
})

// Breadcrumb Schema
export const getBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>) => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: breadcrumbs.map((item, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: item.name,
    item: item.url,
  })),
})

// FAQ Schema
export const getFAQSchema = (faqs: Array<{ question: string; answer: string }>) => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: faqs.map((faq) => ({
    '@type': 'Question',
    name: faq.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: faq.answer,
    },
  })),
})

// Product Schema for NFTs
export const getProductSchema = (nft: any) => ({
  '@context': 'https://schema.org',
  '@type': 'Product',
  name: nft.name,
  description: nft.description,
  image: nft.imageUrl,
  brand: {
    '@type': 'Brand',
    name: pageHeadConfigDefault.siteName,
  },
  category: 'Digital Art',
  offers: nft.price ? {
    '@type': 'Offer',
    price: nft.price,
    priceCurrency: 'ETH',
    availability: 'https://schema.org/InStock',
    seller: {
      '@type': 'Organization',
      name: pageHeadConfigDefault.siteName,
    },
  } : undefined,
})

// Generate combined structured data for homepage
export const getHomepageStructuredData = () => {
  const organization = getOrganizationSchema()
  const website = getWebsiteSchema()
  
  return {
    '@context': 'https://schema.org',
    '@graph': [organization, website],
  }
}
