import React from 'react'
import { Form, Input } from 'antd'
import { useRouter } from 'next/router'
import UiButton from '@/components/ui/Button'
import { usePost } from '@/hooks/usePost'
import { postContact } from '@/services/apiCall/contact'
import { CONTACT_CONFIG } from '@/constants/contact'
import { useTranslate } from '@/hooks/useTranslate'
import SEOHead from '@/components/seo/SEOHead'
import { getPageSeoConfig } from '@/utils/seoI18n'

interface ContactFormValues {
  fullName: string
  companyName?: string
  email: string
  phone: string
  subject: string
  details: string
}

export default function ContactPage() {
  const { locale } = useRouter()
  const t = useTranslate()
  const [form] = Form.useForm()
  const seoConfig = getPageSeoConfig('contact', locale)
  const { mutate, isPending } = usePost({
    queryKey: ['contact'],
    callback: postContact,
  })

  const handleFinish = (values: ContactFormValues) => {
    mutate(values, {
      onSuccess: () => {
        form.resetFields()
      },
    })
  }

  const InputLabel = ({
    title,
    required = true,
  }: {
    title: string
    required?: boolean
  }) => (
    <span className="font-bold text-white text-sm">
      {title}
      {required && <span className="text-red-500"> *</span>}
    </span>
  )

  const inputClasses =
    '!bg-transparent border border-[#333333] px-3 py-2 text-white placeholder:text-[#888888] focus:border-[#FFD15C]'

  return (
    <>
      <SEOHead
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        type="website"
      />
      <h2 className="font-montserrat font-semibold sm:text-[48px] text-[32px] leading-[120%] tracking-[0.02em] text-white sm:mb-[50px] mb-[35px] mt-[40px]">
        {t[CONTACT_CONFIG.contactTitle]}
      </h2>
      <div className="bg-getCardBg px-5 py-10 rounded-lg flex flex-col md:flex-row gap-10 text-white font-montserrat">
        {/* Contact Information Panel */}
        <div className="flex-1 md:mb-0 sm:space-y-10 space-y-5">
          <p className="font-medium text-[14px] leading-[170%] tracking-[0.02em]">
            {t[CONTACT_CONFIG.contactSubtitle]}
          </p>
          <div>
            <h3 className="font-bold text-sm leading-[170%] tracking-[0.02em] mb-5">
              {t[CONTACT_CONFIG.contactRequiredInfoTitle]}
            </h3>
            <ul className="list-disc pl-5 sm:space-y-2 font-medium text-[14px] leading-[170%] tracking-[0.02em]">
              <li>{t[CONTACT_CONFIG.fullnameTitle]}</li>
              <li>{t[CONTACT_CONFIG.companyName1Title]}</li>
              <li>{t[CONTACT_CONFIG.emailTitle]}</li>
              <li>{t[CONTACT_CONFIG.phoneTitle]}</li>
              <li>{t[CONTACT_CONFIG.subjectTitle]}</li>
              <li>{t[CONTACT_CONFIG.inquiryDetailsTitle]}</li>
            </ul>
          </div>
          <p className="font-medium text-[14px] leading-[170%] tracking-[0.02em]">
            {t[CONTACT_CONFIG.note]}
          </p>
        </div>
        {/* Contact Form Panel */}
        <div className="flex-1">
          <Form
            layout="vertical"
            className="sm:space-y-10 space-y-5"
            form={form}
            onFinish={handleFinish}
            requiredMark={false}
          >
            <Form.Item
              label={<InputLabel title={t[CONTACT_CONFIG.fullnameTitle]} />}
              name="fullname"
              rules={[
                {
                  required: true,
                  message: t[CONTACT_CONFIG.contactFullnameRequired],
                },
                {
                  max: 255,
                  message: t[CONTACT_CONFIG.contactFullnameMaxLength],
                },
                {
                  validator: (_, value) => {
                    if (value && value.trim().length === 0) {
                      return Promise.reject(
                        t[CONTACT_CONFIG.fullnameCannotBeOnlySpaces]
                      )
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Input
                maxLength={255}
                className={inputClasses}
                placeholder={t[CONTACT_CONFIG.fullnamePlaceholder]}
                allowClear
                showCount
              />
            </Form.Item>
            <Form.Item
              label={
                <InputLabel
                  title={t[CONTACT_CONFIG.companyName2Title]}
                  required={false}
                />
              }
              name="company_name"
              rules={[
                {
                  max: 255,
                  message: t[CONTACT_CONFIG.companyNameMaxLength],
                },
                {
                  validator: (_, value) => {
                    if (value && value.trim().length === 0) {
                      return Promise.reject(
                        t[CONTACT_CONFIG.companyNameCannotBeOnlySpaces]
                      )
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Input
                maxLength={255}
                className={inputClasses}
                placeholder={t[CONTACT_CONFIG.contactCompanyNameOptional]}
                allowClear
                showCount
              />
            </Form.Item>
            <Form.Item
              label={<InputLabel title={t[CONTACT_CONFIG.emailTitle]} />}
              name="email"
              rules={[
                {
                  required: true,
                  message: t[CONTACT_CONFIG.contactEmailRequired],
                },
                {
                  type: 'email',
                  message: t[CONTACT_CONFIG.contactEmailInvalid],
                },
                {
                  max: 255,
                  message: t[CONTACT_CONFIG.contactEmailMaxLength],
                },
              ]}
            >
              <Input
                maxLength={255}
                className={inputClasses}
                placeholder={t[CONTACT_CONFIG.emailPlaceholder]}
                allowClear
                showCount
              />
            </Form.Item>
            <Form.Item
              label={
                <div className="flex items-end">
                  <InputLabel title={t[CONTACT_CONFIG.phoneTitle]} />
                  <span className="text-red-500 text-xs ml-2">
                    {t[CONTACT_CONFIG.phoneNote]}
                  </span>
                </div>
              }
              name="phone_number"
              rules={[
                {
                  required: true,
                  message: t[CONTACT_CONFIG.contactPhoneRequired],
                },
                {
                  pattern: /^\+?[0-9]{10,13}$/,
                  message: t[CONTACT_CONFIG.contactPhoneInvalid],
                },
                {
                  max: 20,
                  message: t[CONTACT_CONFIG.contactPhoneMaxLength],
                },
              ]}
            >
              <Input
                maxLength={20}
                className={inputClasses}
                placeholder={t[CONTACT_CONFIG.phonePlaceholder]}
                allowClear
              />
            </Form.Item>
            <Form.Item
              label={<InputLabel title={t[CONTACT_CONFIG.subjectTitle]} />}
              name="subject"
              rules={[
                {
                  required: true,
                  message: t[CONTACT_CONFIG.contactSubjectRequired],
                },
                {
                  max: 255,
                  message: t[CONTACT_CONFIG.subjectMaxLength],
                },
                {
                  validator: (_, value) => {
                    if (value && value.trim().length === 0) {
                      return Promise.reject(
                        t[CONTACT_CONFIG.subjectCannotBeOnlySpaces]
                      )
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Input
                maxLength={255}
                className={inputClasses}
                placeholder={t[CONTACT_CONFIG.subjectPlaceholder]}
                allowClear
                showCount
              />
            </Form.Item>
            <Form.Item
              label={
                <InputLabel title={t[CONTACT_CONFIG.inquiryDetailsTitle]} />
              }
              name="inquiry_details"
              rules={[
                {
                  required: true,
                  message: t[CONTACT_CONFIG.contactInquiryDetailsRequired],
                },
                {
                  max: 1000,
                  message: t[CONTACT_CONFIG.inquiryDetailsMaxLength],
                },
                {
                  validator: (_, value) => {
                    if (value && value.trim().length === 0) {
                      return Promise.reject(
                        t[CONTACT_CONFIG.inquiryDetailsCannotBeOnlySpaces]
                      )
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Input.TextArea
                maxLength={1000}
                rows={5}
                className="!bg-transparent border border-[#333333] px-3 py-2 text-white placeholder:text-[#888888] focus:border-[#FFD15C] resize-none"
                placeholder={t[CONTACT_CONFIG.inquiryDetailsPlaceholder]}
                allowClear
                showCount
              />
            </Form.Item>
            <Form.Item>
              <UiButton
                title={
                  isPending
                    ? t[CONTACT_CONFIG.submitting]
                    : t[CONTACT_CONFIG.submit]
                }
                className="w-full h-[44px]"
                isFilled={true}
                isBorder={false}
                isDisabled={isPending}
                handleClick={form.submit}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </>
  )
}
