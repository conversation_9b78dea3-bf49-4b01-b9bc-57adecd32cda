import React, { useMemo, useState } from 'react'
import {
  BackgroundCollection,
  ListNfts,
  Statistics,
} from '@/components/collection/detail'
import { useRouter } from 'next/router'
import { useGet } from '@/hooks/useGet'
import { getCollectionForUserById } from '@/services/apiCall/collections'
import { getCollectionNftsForUser } from '@/services/apiCall'
import Spinner from '@/components/ui/Spinner'
import { Input } from 'antd'
import { IconSearch } from '@/icons'
import UiButton from '@/components/ui/Button'
import Link from 'next/link'
import Breadcrumbs from '@/components/breadcrumbs/Breadcrumbs'
import { useTranslate } from '@/hooks/useTranslate'
import { COLLECTION_DETAIL_CONFIG } from '@/constants/collection-detail'
import SEOHead from '@/components/seo/SEOHead'
import { getPageSeoConfig } from '@/utils/seoI18n'
import { getCurrentLanguage } from '@/utils'

export default function CollectionDetail() {
  const router = useRouter()
  const storedLang = getCurrentLanguage();
  const { collectionId: id } = router.query
  const t = useTranslate()

  const isReady = router.isReady && typeof id === 'string'

  const [inputValue, setInputValue] = useState('')
  const [searchWord, setSearchWord] = useState('')
  const [page, setPage] = useState<number>(1)

  const filter = useMemo(() => {
    return {
      pageIndex: page,
      ...(searchWord ? { searchWord } : {}),
    }
  }, [page, searchWord])

  const { data: collection, isLoading: collectionLoading } = useGet({
    queryKey: ['user-collection', id],
    callback: () => getCollectionForUserById(id as string),
    enabled: isReady,
  })

  const { data: nftData, isLoading: nftLoading } = useGet({
    queryKey: ['user-collection-nfts', id, filter],
    callback: () => getCollectionNftsForUser(filter, id as string),
    enabled: isReady,
  })

  const nfts = nftData?.items || []

  if (!isReady || collectionLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <Spinner size="h-12 w-12" />
        <p className="mt-4 text-gray-400">
          {t[COLLECTION_DETAIL_CONFIG.loadingCollection]}
        </p>
      </div>
    )
  }

  if (!collection) {
    return (
      <div className="text-center text-red-500">
        <p className="text-xl mb-4">
          {t[COLLECTION_DETAIL_CONFIG.collectionNotFound]}
        </p>
        <Link href="/collections">
          <UiButton
            title={t[COLLECTION_DETAIL_CONFIG.backToCollections]}
            className="mt-4"
          />
        </Link>
      </div>
    )
  }

  // Create meta description from collection description
  const metaDescription = collection.description?.length > 160
    ? collection.description.substring(0, 157) + '...'
    : collection.description || `Explore ${collection.name} NFT collection on GLITTERS marketplace`

  // Get localized SEO config for collection detail
  const baseSeoConfig = getPageSeoConfig('collectionDetail', storedLang, {
    collectionName: collection.name,
    standard: collection.standard || 'ERC-721'
  })

  return (
    <>
      <SEOHead
        title={baseSeoConfig.title}
        description={metaDescription || baseSeoConfig.description}
        keywords={baseSeoConfig.keywords}
        type="website"
        image={collection.logoImage || collection.backgroundImage}
      />
      <div>
        <Breadcrumbs dynamicNames={[collection.name]} />

      <BackgroundCollection
        backgroundImage={collection.backgroundImage}
        collectionIcon={collection.logoImage}
        name={collection.name}
        standard={collection.standard}
        description={collection.description}
        className="my-[40px]"
      />

      <Statistics className="h-[200px]" collection={collection} />
      <div className="flex items-center gap-4 mb-[30px]">
        <Input
          size="large"
          placeholder={t[COLLECTION_DETAIL_CONFIG.searchPlaceholder]}
          suffix={<IconSearch height={20} width={20} />}
          className="sm:w-[300px]"
          allowClear
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onPressEnter={() => setSearchWord(inputValue.trim())}
        />
        <UiButton
          title={t[COLLECTION_DETAIL_CONFIG.searchButton]}
          className="h-[44px] sm:w-150px"
          isFilled={true}
          handleClick={() => setSearchWord(inputValue.trim())}
        />
      </div>
      <ListNfts
        collectionId={id}
        nfts={nfts}
        loading={nftLoading}
        total={nftData?.totalItems}
        current={nftData?.pageIndex}
        pageSize={nftData?.pageSize}
        onPageChange={setPage}
      />
      </div>
    </>
  )
}
